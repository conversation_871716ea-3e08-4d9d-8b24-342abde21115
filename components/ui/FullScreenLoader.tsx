"use client";
import { cn } from "@/lib/utils";
import Loader from "./loader";

interface FullScreenLoaderProps {
  text?: string;
  className?: string;
}

export function FullScreenLoader({
  text = "Loading...",
  className
}: FullScreenLoaderProps) {
  return (
    <div
      className={cn(
        "fixed inset-0 bg-background z-50 flex items-center justify-center",
        className
      )}
    >
      <div className="flex items-center gap-3">
        {/* Same spinner style as sign-in button */}
        <Loader />
        {text && (
          <span className="text-sm font-medium text-foreground">
            {text}
          </span>
        )}
      </div>
    </div>
  );
}

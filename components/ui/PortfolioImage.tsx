import { useIsExport } from '@/contexts/ExportContext';
import NextImage from 'next/image';

interface PortfolioImageProps {
    // --- 2. Remove isExport from the props ---
    src: string;
    alt: string;
    className?: string;
    width?: number;
    height?: number;
    priority?: boolean;
    fill?: boolean;
    [key: string]: unknown;
}

export function PortfolioImage(props: PortfolioImageProps) {
    const isExport = useIsExport();

    // If we are exporting to static HTML, render a standard <img> tag.
    // This uses the direct Cloudinary/placeholder URL and will work anywhere.
    if (isExport) {
        // We filter out Next.js specific props that are not valid for a standard img tag.
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { fill, priority, ...rest } = props;
        // eslint-disable-next-line @next/next/no-img-element
        return <img {...rest} alt={props.alt} />;
    }

    // Otherwise (on your live Next.js site), render the optimized <Image /> component.
    return <NextImage {...props} />;
}
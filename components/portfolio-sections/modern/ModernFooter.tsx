"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { Heart, Code, Sparkles } from "lucide-react";

export function ModernFooter({ isEditing, serverData }: SectionProps) {
    const context = useEditor();
    const data = isEditing ? context.state.formData : serverData!;
    const currentYear = new Date().getFullYear();

    return (
        <footer className="modern-footer">
            {/* Background decoration */}
            <div className="absolute inset-0 pointer-events-none">
                <div className="absolute bottom-0 left-20 w-64 h-64 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full blur-3xl"></div>
                <div className="absolute bottom-0 right-20 w-48 h-48 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-full blur-3xl"></div>
            </div>

            <div className="relative z-10 modern-container py-12">
                <div className="flex flex-col items-center text-center space-y-8">
                    {/* Main content */}
                    <div className="space-y-4">
                        <h3 className="modern-footer-title modern-gradient-text-primary">
                            {data.userName || "Portfolio"}
                        </h3>

                        <p className="modern-footer-subtitle modern-gradient-text-secondary">
                            {data.profession || "Creative Professional"}
                        </p>
                    </div>

                    {/* Divider */}
                    <div className="w-24 h-px bg-gradient-to-r from-transparent via-gray-600 to-transparent"></div>

                    {/* Copyright and powered by */}
                    <div className="space-y-4 text-center">
                        <p className="modern-footer-text">
                            © {currentYear} {data.userName || "Portfolio"}. All rights reserved.
                        </p>

                        {/* Powered by section */}
                        <div className="modern-footer-brand group">
                            <span>Built with</span>
                            <Heart className="h-4 w-4 text-red-500 group-hover:animate-pulse" />
                            <span>on</span>
                            <div className="flex items-center gap-1">
                                <Code className="h-4 w-4 text-purple-400" />
                                <span className="modern-footer-brand-name modern-gradient-text-accent">
                                    Profolify
                                </span>
                                <Sparkles className="h-4 w-4 text-cyan-400" />
                            </div>
                        </div>
                    </div>

                    {/* Back to top hint */}
                    <div className="pt-4">
                        <a 
                            href="#" 
                            className="inline-flex items-center gap-2 text-xs text-gray-500 hover:text-purple-400 transition-colors duration-300 group"
                        >
                            <div className="w-6 h-6 rounded-full border border-gray-700 flex items-center justify-center group-hover:border-purple-400 transition-colors duration-300">
                                <svg className="w-3 h-3 transform group-hover:-translate-y-0.5 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                                </svg>
                            </div>
                            <span className="group-hover:text-purple-400 transition-colors duration-300">Back to top</span>
                        </a>
                    </div>
                </div>
            </div>

            {/* Bottom gradient line */}
            <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-purple-500/50 to-transparent"></div>
        </footer>
    );
}

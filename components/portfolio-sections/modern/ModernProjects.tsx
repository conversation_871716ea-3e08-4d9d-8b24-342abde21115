"use client";
import { EditableText } from "@/components/ui/EditableText";
import { Button } from "@/components/ui/button";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "../../portfolio-themes/types";
import { Upload, Trash, ExternalLink } from "lucide-react";
import { PortfolioImage } from "@/components/ui/PortfolioImage";
import { useIsExport } from "@/contexts/ExportContext";
import { Project } from "@/lib/types";

export function ModernProjects({ isEditing, serverData, onImageUpload }: SectionProps) {
    const context = useEditor();
    const isExport = useIsExport();

    const data = isEditing ? context.state.formData : serverData!;
    const dispatch = isEditing ? context.dispatch : null;

    const handleUpdate = (index: number, field: keyof Project, value: string) => {
        if (dispatch) dispatch({ type: 'UPDATE_PROJECT', payload: { index, field, value } });
    };

    return (
        <section id="projects" className="relative px-4 sm:px-6 lg:px-8 py-20 lg:py-32 bg-gray-950 text-white overflow-hidden">
            {/* Background elements */}
            <div className="absolute inset-0">
                {/* Animated grid */}
                <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:80px_80px] animate-pulse"></div>

                {/* Gradient orbs */}
                <div className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full blur-3xl"></div>
                <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-full blur-3xl"></div>
            </div>

            <div className="container mx-auto px-4 relative z-10">
                {/* Section header */}
                <div className="text-center mb-20">
                    <h2 className={`text-4xl sm:text-5xl lg:text-6xl font-black mb-6 ${
                        isExport
                            ? 'text-white'
                            : 'bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent'
                    }`}>
                        Featured Work
                    </h2>
                    <p className="text-xl text-gray-400 max-w-2xl mx-auto leading-relaxed">
                        Showcasing projects that demonstrate creativity, technical expertise, and innovative solutions
                    </p>
                    <div className="w-24 h-1 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto rounded-full mt-6"></div>
                </div>

                {/* Projects grid */}
                <div className="space-y-24 lg:space-y-32">
                    {(data.projects || []).map((project, index) => {
                        const isUploading = isEditing && context!.state.isUploading?.type === 'project' && context!.state.isUploading?.id === project.id;
                        const isEven = index % 2 === 0;

                        return (
                            <div key={project.id} className="group">
                                <div className={`grid lg:grid-cols-2 gap-12 lg:gap-16 items-center ${!isEven ? 'lg:grid-flow-col-dense' : ''}`}>
                                    {/* Project Image */}
                                    <div className={`relative ${!isEven ? 'lg:col-start-2' : ''}`}>
                                        <div className="relative aspect-[4/3] rounded-2xl overflow-hidden bg-gray-900 group-hover:shadow-2xl group-hover:shadow-purple-500/20 transition-all duration-500">
                                            {/* Glowing border effect */}
                                            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 via-pink-500/20 to-cyan-500/20 rounded-2xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                                            {/* Loading overlay - shows when uploading */}
                                            {isUploading && (
                                                <div className="absolute inset-0 bg-black/95 flex items-center justify-center z-40 backdrop-blur-sm rounded-2xl">
                                                    <div className="flex flex-col items-center gap-4 text-white">
                                                        <div className="relative">
                                                            <div className="w-16 h-16 border-4 border-purple-500/30 rounded-full"></div>
                                                            <div className="absolute inset-0 w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
                                                        </div>
                                                        <div className="text-center">
                                                            <p className="text-lg font-semibold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Uploading Image</p>
                                                            <p className="text-sm text-gray-300">Please wait...</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            )}

                                            <div className="relative z-10 w-full h-full rounded-2xl overflow-hidden">
                                                <PortfolioImage
                                                    key={`modern-project-${project.id}-${project.imageUrl || 'placeholder'}-${isUploading ? 'uploading' : 'ready'}`}
                                                    src={project.imageUrl || 'https://placehold.co/800x600/1f2937/9ca3af?text=Project'}
                                                    alt={project.title}
                                                    fill
                                                    className={`object-cover transition-all duration-700 group-hover:scale-110 ${isUploading ? 'opacity-30' : 'opacity-100'}`}
                                                />

                                                {/* Overlay gradient */}
                                                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                            </div>

                                            {isEditing && !isUploading && (
                                                <label htmlFor={`project-image-${project.id}`} className="absolute inset-0 bg-black/80 flex flex-col items-center justify-center text-white opacity-0 group-hover:opacity-100 cursor-pointer transition-all duration-300 backdrop-blur-sm rounded-2xl z-20">
                                                    <div className="flex flex-col items-center gap-3">
                                                        <Upload className="h-8 w-8" />
                                                        <span className="text-sm font-medium">Change Image</span>
                                                    </div>
                                                    <input
                                                        key={`modern-project-input-${project.id}`}
                                                        id={`project-image-${project.id}`}
                                                        type="file"
                                                        className="hidden"
                                                        accept="image/*"
                                                        onChange={(e) => {
                                                            if (e.target.files) {
                                                                onImageUpload!({ file: e.target.files[0], type: 'project', id: project.id });
                                                                // Reset the input value to allow re-uploading the same file
                                                                e.target.value = '';
                                                            }
                                                        }}
                                                        disabled={isUploading}
                                                    />
                                                </label>
                                            )}
                                        </div>
                                    </div>

                                    {/* Project Content */}
                                    <div className={`space-y-6 ${!isEven ? 'lg:col-start-1' : ''}`}>
                                        <div className="space-y-4">
                                            <div className="flex items-center gap-3">
                                                <div className="w-8 h-px bg-gradient-to-r from-purple-500 to-pink-500"></div>
                                                <span className="text-sm font-medium text-gray-400 uppercase tracking-wider">
                                                    Project {String(index + 1).padStart(2, '0')}
                                                </span>
                                            </div>

                                            <EditableText
                                                isEditing={isEditing}
                                                tagName="h3"
                                                className={`text-3xl lg:text-4xl font-bold leading-tight ${
                                                    isExport
                                                        ? 'text-white'
                                                        : 'bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent'
                                                }`}
                                                initialValue={project.title}
                                                onSave={(v) => handleUpdate(index, 'title', v)}
                                            />
                                        </div>

                                        <EditableText
                                            isEditing={isEditing}
                                            tagName="div"
                                            className="text-lg text-gray-300 leading-relaxed"
                                            initialValue={project.description}
                                            onSave={(v) => handleUpdate(index, 'description', v)}
                                        />

                                        <div className="flex flex-col sm:flex-row gap-4 pt-4">
                                            {isEditing ? (
                                                <div className="space-y-2">
                                                    <label className="text-xs font-semibold text-gray-400 uppercase tracking-wider">Project URL</label>
                                                    <EditableText
                                                        isEditing={isEditing}
                                                        tagName="p"
                                                        className="text-sm text-purple-400 bg-gray-900/50 px-3 py-2 rounded-lg border border-gray-800"
                                                        initialValue={project.url || "https://example.com"}
                                                        onSave={(v) => handleUpdate(index, 'url', v)}
                                                    />
                                                </div>
                                            ) : project.url && (
                                                <Button asChild variant="outline" size="lg" className="border-gray-700 bg-gray-900/50 hover:bg-gray-800 hover:border-purple-500 text-white backdrop-blur-sm transition-all duration-300">
                                                    <a href={project.url} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2">
                                                        View Project
                                                        <ExternalLink className="h-4 w-4" />
                                                    </a>
                                                </Button>
                                            )}

                                            {isEditing && (
                                                <Button
                                                    variant="destructive"
                                                    size="lg"
                                                    className="bg-red-900/50 hover:bg-red-800 border border-red-800 hover:border-red-700 backdrop-blur-sm transition-all duration-300"
                                                    onClick={() => dispatch!({ type: 'DELETE_PROJECT', payload: { id: project.id } })}
                                                >
                                                    <Trash className="h-4 w-4 mr-2" />
                                                    Delete Project
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>

                {/* Add project button */}
                {isEditing && (
                    <div className="text-center mt-20">
                        <Button
                            onClick={() => dispatch!({ type: 'ADD_PROJECT' })}
                            size="lg"
                            className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
                        >
                            Add New Project
                        </Button>
                    </div>
                )}
            </div>
        </section>
    );
}
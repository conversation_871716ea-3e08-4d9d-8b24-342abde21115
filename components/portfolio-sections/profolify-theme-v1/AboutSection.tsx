"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "@/components/portfolio-themes/types";
import { EditableText } from "@/components/ui/EditableText";
import { PortfolioData } from "@/lib/types";
import { useIsExport } from "@/contexts/ExportContext";

export function AboutSection({ isEditing, serverData }: SectionProps) {
  const context = useEditor();
  const isExport = useIsExport();

  const data = isEditing ? context.state.formData : serverData!;
  const dispatch = isEditing ? context.dispatch : null;

  const handleUpdate = (field: keyof Omit<PortfolioData, 'projects'>, value: string) => {
    if (dispatch) dispatch({ type: 'UPDATE_FIELD', payload: { field, value } });
  };

  return (
    <section id="about" className="relative py-20 lg:py-32 bg-gradient-to-b from-muted/20 to-background overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-primary/5 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-primary/5 rounded-full blur-2xl"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-4xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-6">
              <div className="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center">
                <div className="w-4 h-4 bg-primary rounded-full"></div>
              </div>
            </div>
            <h2 className={`text-3xl sm:text-4xl lg:text-5xl font-bold mb-4 ${
              isExport
                ? 'text-foreground'
                : 'bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent'
            }`}>
              About Me
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-primary to-primary/60 mx-auto rounded-full"></div>
          </div>

          {/* Content */}
          <div className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-2xl p-4 sm:p-6 lg:p-8 xl:p-12 shadow-xl">
            <EditableText
              isEditing={isEditing}
              tagName="div"
              className="text-base sm:text-lg lg:text-xl text-muted-foreground leading-relaxed text-center lg:text-left space-y-4"
              initialValue={data.about || "Tell your story here. Share your background, experiences, and what drives you. This is your opportunity to connect with visitors on a personal level."}
              onSave={(val) => handleUpdate('about', val)}
            />
          </div>

          {/* Stats or highlights could go here in the future */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
            <div className="text-center p-6 bg-card/30 rounded-xl border border-border/30">
              <div className="text-2xl font-bold text-primary mb-2">Experience</div>
              <div className="text-sm text-muted-foreground">Years of expertise</div>
            </div>
            <div className="text-center p-6 bg-card/30 rounded-xl border border-border/30">
              <div className="text-2xl font-bold text-primary mb-2">Projects</div>
              <div className="text-sm text-muted-foreground">Completed works</div>
            </div>
            <div className="text-center p-6 bg-card/30 rounded-xl border border-border/30">
              <div className="text-2xl font-bold text-primary mb-2">Passion</div>
              <div className="text-sm text-muted-foreground">Driven by excellence</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
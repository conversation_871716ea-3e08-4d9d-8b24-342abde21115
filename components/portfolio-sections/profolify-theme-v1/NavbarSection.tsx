"use client";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "@/components/portfolio-themes/types";
import { Sheet, <PERSON><PERSON><PERSON>onte<PERSON>, She<PERSON><PERSON>rigger, SheetClose } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Menu } from "lucide-react";

const navLinks = [
  { href: '#about', label: 'About' },
  { href: '#projects', label: 'Projects' },
  { href: '#contact', label: 'Contact' },
];

export function NavbarSection({ isEditing, serverData }: SectionProps) {
  const context = useEditor();
  const data = isEditing ? context.state.formData : serverData!;

  // A single, reusable component for the navigation links
  const NavContent = ({ isMobile = false }: { isMobile?: boolean }) => (
    <>
      {navLinks.map(link => (
        isMobile ? (
          // For the live mobile app, use SheetClose to auto-close the menu
          <SheetClose key={link.href} asChild>
            <a href={link.href} className="block rounded-lg px-4 py-3 text-base font-medium text-muted-foreground hover:bg-primary/10 hover:text-primary transition-all duration-300">
              {link.label}
            </a>
          </SheetClose>
        ) : (
          // For desktop and static export, use a simple link
          <a key={link.href} href={link.href} className="relative text-sm font-medium text-muted-foreground transition-all duration-300 hover:text-primary after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-0 after:bg-primary after:transition-all after:duration-300 hover:after:w-full">
            {link.label}
          </a>
        )
      ))}
    </>
  );

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/50 bg-background/80 backdrop-blur-xl supports-[backdrop-filter]:bg-background/60 shadow-sm">
      <div className="container h-16 flex items-center justify-between px-4 sm:px-6">
        {/* Logo/Brand */}
        <a href="#" className="group flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary/60 rounded-lg flex items-center justify-center">
            <div className="w-4 h-4 bg-white rounded-sm"></div>
          </div>
          <span className="font-bold text-lg tracking-tight group-hover:text-primary transition-colors duration-300">
            {data.userName || "Portfolio"}
          </span>
        </a>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex gap-8 items-center">
          <NavContent />
        </nav>

        {/* Mobile Menu */}
        <div className="md:hidden">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="hover:bg-primary/10" data-menu-button>
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-80 bg-background/95 backdrop-blur-xl">
              <div className="flex flex-col h-full">
                {/* Mobile Header */}
                <div className="flex items-center space-x-2 pb-6 border-b border-border/50">
                  <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary/60 rounded-lg flex items-center justify-center">
                    <div className="w-4 h-4 bg-white rounded-sm"></div>
                  </div>
                  <span className="font-bold text-lg tracking-tight">
                    {data.userName || "Portfolio"}
                  </span>
                </div>

                {/* Mobile Navigation */}
                <nav className="flex flex-col gap-2 pt-8">
                  <NavContent isMobile={true} />
                </nav>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      {/* Static Export Mobile Menu Panel */}
      <div data-menu-panel className="hidden md:hidden bg-background/95 backdrop-blur-xl border-b border-border/50">
        <nav className="px-4 pt-4 pb-6 space-y-2">
          {navLinks.map(link => (
            <a
              key={link.href}
              href={link.href}
              data-menu-link
              className="block rounded-lg px-4 py-3 text-base font-medium text-muted-foreground hover:bg-primary/10 hover:text-primary transition-all duration-300"
            >
              {link.label}
            </a>
          ))}
        </nav>
      </div>
    </header>
  );
}
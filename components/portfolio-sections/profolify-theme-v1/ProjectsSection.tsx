"use client";
import { EditableText } from "@/components/ui/EditableText";
import { Button } from "@/components/ui/button";
import { useEditor } from "@/contexts/EditorContext";
import { SectionProps } from "@/components/portfolio-themes/types";
import { Upload, Trash, ExternalLink } from "lucide-react";
import { PortfolioImage } from "../../ui/PortfolioImage";
import { useIsExport } from "@/contexts/ExportContext";

export function ProjectsSection({ isEditing, serverData, onImageUpload }: SectionProps) {
  const context = useEditor();
  const isExport = useIsExport();

  const data = isEditing ? context.state.formData : serverData!;
  const dispatch = isEditing ? context.dispatch : null;

  const handleUpdate = (index: number, field: string, value: string) => {
    if (dispatch) dispatch({ type: 'UPDATE_PROJECT', payload: { index, field, value } });
  };

  const CardContent = ({ project, index }: { project: { id: string; title: string; description: string; imageUrl?: string; url?: string }, index: number }) => {
    const isUploading = isEditing && context!.state.isUploading?.type === 'project' && context!.state.isUploading?.id === project.id;
    return (
      <div className="group bg-card border border-border/50 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 h-full flex flex-col">
        <div className="aspect-video bg-gradient-to-br from-muted to-muted/50 relative group/image overflow-hidden flex-shrink-0">
          {/* Loading overlay - shows when uploading */}
          {isUploading && (
            <div className="absolute inset-0 bg-black/90 flex items-center justify-center z-30 backdrop-blur-sm">
              <div className="flex flex-col items-center gap-4 text-white">
                <div className="relative">
                  <div className="w-16 h-16 border-4 border-primary/30 rounded-full"></div>
                  <div className="absolute inset-0 w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
                </div>
                <div className="text-center">
                  <p className="text-lg font-semibold">Uploading Image</p>
                  <p className="text-sm text-gray-300">Please wait...</p>
                </div>
              </div>
            </div>
          )}

          <PortfolioImage
            key={`project-${project.id}-${project.imageUrl || 'placeholder'}-${Date.now()}`}
            src={project.imageUrl || 'https://placehold.co/600x400/e2e8f0/64748b?text=Project'}
            alt={project.title}
            fill
            className={`object-cover transition-all duration-500 group-hover:scale-110 group-hover:brightness-110 ${isUploading ? 'opacity-30' : 'opacity-100'}`}
          />

          {/* Overlay gradient */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"></div>

          {isEditing && !isUploading && (
            <label htmlFor={`project-image-${project.id}`} className="absolute inset-0 bg-black/70 flex items-center justify-center text-white opacity-0 group-hover/image:opacity-100 cursor-pointer transition-all duration-300 z-20">
              <div className="flex flex-col items-center gap-2">
                <Upload className="h-8 w-8" />
                <span className="text-sm font-medium">Change Image</span>
              </div>
              <input
                key={`profolify-project-input-${project.id}`}
                id={`project-image-${project.id}`}
                type="file"
                className="hidden"
                accept="image/*"
                onChange={(e) => {
                  if (e.target.files) {
                    onImageUpload!({ file: e.target.files[0], type: 'project', id: project.id });
                    // Reset the input value to allow re-uploading the same file
                    e.target.value = '';
                  }
                }}
                disabled={isUploading}
              />
            </label>
          )}

          {/* Project link overlay for non-editing mode */}
          {!isEditing && project.url && project.url !== "https://example.com" && project.url.trim() !== "" && (
            <a
              href={project.url}
              target="_blank"
              rel="noopener noreferrer"
              className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20"
            >
              <Button size="sm" className="bg-white/90 text-black hover:bg-white shadow-lg">
                <ExternalLink className="mr-2 h-4 w-4" />
                View Project
              </Button>
            </a>
          )}
        </div>

        <div className="p-4 sm:p-6 lg:p-8 flex flex-col flex-grow">
          <EditableText
            isEditing={isEditing}
            tagName="h3"
            className="text-lg sm:text-xl lg:text-2xl font-bold mb-2 sm:mb-3 group-hover:text-primary transition-colors duration-300"
            initialValue={project.title}
            onSave={(v) => handleUpdate(index, 'title', v)}
          />
          <EditableText
            isEditing={isEditing}
            tagName="p"
            className="text-sm sm:text-base text-muted-foreground mb-4 sm:mb-6 flex-grow leading-relaxed"
            initialValue={project.description}
            onSave={(v) => handleUpdate(index, 'description', v)}
          />

          {isEditing ? (
            <div className="space-y-2">
              <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Project URL</label>
              <EditableText
                isEditing={isEditing}
                tagName="p"
                className="text-xs sm:text-sm text-primary font-medium"
                initialValue={project.url || "https://example.com"}
                onSave={(v) => handleUpdate(index, 'url', v)}
              />
            </div>
          ) : project.url && project.url !== "https://example.com" && project.url.trim() !== "" && (
            <Button asChild variant="outline" size="sm" className="border-border bg-card hover:bg-muted text-foreground hover:text-primary transition-all duration-300">
              <a href={project.url} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2">
                View Project
                <ExternalLink className="h-4 w-4" />
              </a>
            </Button>
          )}
        </div>
      </div>
    );
  };

  return (
    <section id="projects" className="relative py-20 lg:py-32 bg-gradient-to-b from-background to-muted/20 overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-40 right-20 w-64 h-64 bg-primary/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-40 left-20 w-48 h-48 bg-primary/5 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-6">
            <div className="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center">
              <div className="w-4 h-4 bg-primary rounded-full"></div>
            </div>
          </div>
          <h2 className={`text-3xl sm:text-4xl lg:text-5xl font-bold mb-4 ${
            isExport
              ? 'text-foreground'
              : 'bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent'
          }`}>
            Featured Projects
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Explore my latest work and creative solutions
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-primary to-primary/60 mx-auto rounded-full mt-6"></div>
        </div>

        {/* Projects Grid - Responsive with justified spacing */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-10 justify-items-center">
          {(data.projects || []).map((project, index) => (
            <div key={project.id} className="relative group/card w-full max-w-sm">
              {isEditing && (
                <Button
                  variant="destructive"
                  size="icon"
                  className="absolute -top-2 -right-2 z-30 h-8 w-8 opacity-0 group-hover/card:opacity-100 transition-opacity duration-300 shadow-lg"
                  onClick={() => dispatch!({ type: 'DELETE_PROJECT', payload: { id: project.id } })}
                >
                  <Trash className="h-4 w-4" />
                </Button>
              )}
              <CardContent project={project} index={index} />
            </div>
          ))}
        </div>

        {/* Add Project Button */}
        {isEditing && (
          <div className="text-center mt-16">
            <Button
              onClick={() => dispatch!({ type: 'ADD_PROJECT' })}
              size="lg"
              className="bg-primary hover:bg-primary/90 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              Add New Project
            </Button>
          </div>
        )}
      </div>
    </section>
  );
}
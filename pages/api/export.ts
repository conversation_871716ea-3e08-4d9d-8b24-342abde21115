import type { NextApiRequest, NextApiResponse } from 'next';
import { renderToStaticMarkup } from 'react-dom/server';
import J<PERSON><PERSON><PERSON> from 'jszip';
import { getPortfolio } from '@/lib/portfolio-api';
import { auth } from '@/lib/firebase-admin';
import { ProfolifyTheme } from '@/components/portfolio-themes/ProfolifyTheme';
import { ModernTheme } from '@/components/portfolio-themes/ModernTheme';
import React from 'react';
import { ExportProvider } from '@/contexts/ExportContext';

// Helper to get the theme component
const getThemeComponent = (templateId: string) => {
    return templateId === 'modern-theme-v1' ? ModernTheme : ProfolifyTheme;
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', 'POST');
        return res.status(405).end('Method Not Allowed');
    }

    try {
        const token = req.headers.authorization?.split('Bearer ')[1];
        if (!token) { return res.status(401).json({ error: 'Unauthorized' }); }
        const decodedToken = await auth.verifyIdToken(token);
        const userId = decodedToken.uid;

        const portfolioData = await getPortfolio(userId);
        if (!portfolioData) { return res.status(404).json({ error: 'Portfolio not found' }); }

        const ThemeComponent = getThemeComponent(portfolioData.templateId);
        const staticHtml = renderToStaticMarkup(
            React.createElement(
                ExportProvider,
                { value: true },
                React.createElement(ThemeComponent, { isEditing: false, serverData: portfolioData })
            )
        );

        // --- THIS IS THE SIMPLIFIED AND CORRECTED SCRIPT ---
        const interactivityScript = `
        <script>
            document.addEventListener('DOMContentLoaded', () => {
                const menuButton = document.querySelector('[data-menu-button]');
                const menuPanel = document.querySelector('[data-menu-panel]');
                
                if (menuButton && menuPanel) {
                    menuButton.addEventListener('click', (e) => {
                        // We stop the event to prevent the live app's Sheet from trying to open
                        e.preventDefault();
                        e.stopPropagation();
                        
                        // Toggle a simple 'hidden' class from Tailwind CSS
                        menuPanel.classList.toggle('hidden');
                    });

                    // Close menu when any link inside it is clicked
                    menuPanel.querySelectorAll('[data-menu-link]').forEach(link => {
                        link.addEventListener('click', () => {
                            menuPanel.classList.add('hidden');
                        });
                    });
                }
            });
        </script>
        `;

        const fullHtml = `<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${portfolioData.userName}'s Portfolio</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="root">${staticHtml}</div>
    ${interactivityScript}
</body>
</html>`;

        const cssContent = `
/* Custom CSS for exported portfolio */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: system-ui, -apple-system, sans-serif;
    line-height: 1.6;
}

/* Ensure proper text visibility in exports */
.text-foreground {
    color: #0f172a !important;
}

.text-muted-foreground {
    color: #64748b !important;
}

.text-primary {
    color: #3b82f6 !important;
}

.bg-primary {
    background-color: #3b82f6 !important;
}

.bg-card {
    background-color: #ffffff !important;
}

.bg-background {
    background-color: #ffffff !important;
}

.bg-muted {
    background-color: #f8fafc !important;
}

.border-border {
    border-color: #e2e8f0 !important;
}

/* Fix gradient backgrounds for export */
.bg-gradient-to-br {
    background: linear-gradient(to bottom right, var(--tw-gradient-stops)) !important;
}

.bg-gradient-to-b {
    background: linear-gradient(to bottom, var(--tw-gradient-stops)) !important;
}

.bg-gradient-to-r {
    background: linear-gradient(to right, var(--tw-gradient-stops)) !important;
}

/* Ensure buttons are visible and properly styled */
button, .btn, [role="button"] {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0.5rem 1rem !important;
    border-radius: 0.375rem !important;
    font-weight: 500 !important;
    transition: all 0.2s !important;
    text-decoration: none !important;
    border: none !important;
    cursor: pointer !important;
}

/* Specific button size classes */
.h-12 {
    height: 3rem !important;
}

.min-w-\\[160px\\] {
    min-width: 160px !important;
}

/* Fix hover states for export */
a:hover, button:hover {
    opacity: 0.8 !important;
}

/* Ensure text is visible in all contexts */
h1, h2, h3, h4, h5, h6, p, span, div, a {
    color: inherit !important;
}

/* Fix specific text color classes */
.text-4xl, .text-5xl, .text-6xl {
    font-size: inherit !important;
    font-weight: bold !important;
}

/* Ensure proper spacing */
.space-y-4 > * + * {
    margin-top: 1rem !important;
}

.gap-4 {
    gap: 1rem !important;
}

.mb-4 {
    margin-bottom: 1rem !important;
}

.mb-8 {
    margin-bottom: 2rem !important;
}

.mb-12 {
    margin-bottom: 3rem !important;
}

/* Responsive utilities */
@media (max-width: 640px) {
    .container {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }
}

/* Modern Theme Custom Styles */
.modern-theme {
    background-color: #000000 !important;
    color: #ffffff !important;
    font-family: system-ui, -apple-system, sans-serif !important;
}

.modern-gradient-text-primary {
    background: linear-gradient(to right, #ffffff, #f3f4f6, #d1d5db) !important;
    -webkit-background-clip: text !important;
    background-clip: text !important;
    color: transparent !important;
    font-weight: 900 !important;
}

.modern-gradient-text-secondary {
    background: linear-gradient(to right, #a855f7, #ec4899, #06b6d4) !important;
    -webkit-background-clip: text !important;
    background-clip: text !important;
    color: transparent !important;
}

.modern-gradient-text-accent {
    background: linear-gradient(to right, #8b5cf6, #ec4899) !important;
    -webkit-background-clip: text !important;
    background-clip: text !important;
    color: transparent !important;
    font-weight: 600 !important;
}

.modern-button-primary {
    background: linear-gradient(to right, #9333ea, #db2777) !important;
    color: #ffffff !important;
    padding: 0.75rem 2rem !important;
    border-radius: 0.5rem !important;
    font-weight: 600 !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    transition: all 0.3s ease !important;
    border: none !important;
    cursor: pointer !important;
    min-width: 180px !important;
    justify-content: center !important;
}

.modern-container {
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 0 1rem !important;
}

.modern-footer {
    background-color: #000000 !important;
    border-top: 1px solid #374151 !important;
    position: relative !important;
    overflow: hidden !important;
}

.modern-footer-title {
    font-size: 1.5rem !important;
    font-weight: 700 !important;
    color: #ffffff !important;
}

.modern-footer-subtitle {
    font-size: 1.125rem !important;
}

.modern-footer-text {
    color: #9ca3af !important;
    font-size: 0.875rem !important;
}

.modern-footer-brand {
    color: #6b7280 !important;
    font-size: 0.875rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    justify-content: center !important;
}

.modern-footer-brand-name {
    font-weight: 600 !important;
}

.modern-project-title {
    font-size: 1.875rem !important;
    font-weight: 700 !important;
    line-height: 1.2 !important;
    color: #ffffff !important;
}

.modern-project-description {
    font-size: 1.125rem !important;
    color: #d1d5db !important;
    line-height: 1.6 !important;
}

@media (min-width: 640px) {
    .modern-container {
        padding: 0 1.5rem !important;
    }
    .modern-footer-title {
        font-size: 1.875rem !important;
    }
}

@media (min-width: 1024px) {
    .modern-container {
        padding: 0 2rem !important;
    }
    .modern-project-title {
        font-size: 2.25rem !important;
    }
}
`;
        const zip = new JSZip();
        zip.file("index.html", fullHtml);
        zip.file("styles.css", cssContent);
        zip.file("README.md", "Portfolio generated by Profolify.");

        const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });

        res.setHeader('Content-Type', 'application/zip');
        res.setHeader('Content-Disposition', `attachment; filename="${portfolioData.slug || 'portfolio'}.zip"`);
        return res.status(200).send(zipBuffer);

    } catch (error: unknown) {
        console.error("Export failed:", error);
        return res.status(500).json({ error: 'Failed to export portfolio' });
    }
}
"use client";

import { useEffect, useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/stores/auth-store";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useDebounce } from "@/hooks/use-debounce";
import { PortfolioData } from "@/lib/types";
import { getPortfolio, updatePortfolio, deletePortfolio, uploadFile, generateUniqueSlug } from "@/lib/portfolio-api";
import { EditorProvider, useEditor } from "@/contexts/EditorContext";
import { isEqual } from 'lodash';

// UI and Theme Components
import { ProfolifyTheme } from "@/components/portfolio-themes/ProfolifyTheme";
import { ModernTheme } from "@/components/portfolio-themes/ModernTheme";
import { Loader2 } from "lucide-react";
import { ConfirmationDialog } from "@/components/ui/ConfirmationDialog";
import { PortfolioHeader } from "./components/PortfolioHeader";
import { ExportProvider } from "@/contexts/ExportContext";

function PortfolioEditorCore() {
  const { user } = useAuthStore();
  const queryClient = useQueryClient();
  const router = useRouter();
  const { state, dispatch } = useEditor();
  const { formData } = state;

  const debouncedFormData = useDebounce(formData, 1500);
  const serverStateRef = useRef<PortfolioData | null>(null);
  const [isDirty, setIsDirty] = useState(false);

  useEffect(() => {
    if (formData && !serverStateRef.current) {
      serverStateRef.current = formData;
    }
  }, [formData]);

  useEffect(() => {
    if (serverStateRef.current) {
      const hasChanges = !isEqual(formData, serverStateRef.current);
      setIsDirty(hasChanges);
    }
  }, [formData]);

  // --- MUTATIONS ---

  const { mutate: autoSave } = useMutation({
    mutationFn: (data: PortfolioData) => {
      if (!user) throw new Error('User not authenticated');
      return updatePortfolio({ userId: user.uid, data });
    },
    onSuccess: () => {
      // Corrected: The ONLY job of a successful auto-save is to show the status.
      // It does NOT change isDirty or serverStateRef. This keeps the "Update Live"
      // button correctly enabled because the changes are saved but not yet published.
      dispatch({ type: 'SET_SAVE_STATUS', payload: 'saved' });
    },
    onError: () => dispatch({ type: 'SET_SAVE_STATUS', payload: 'error' }),
  });

  const { mutate: publishMutation, isPending: isPublishing } = useMutation({
    mutationFn: async (data: PortfolioData): Promise<PortfolioData> => {
      if (!user) throw new Error('User not authenticated');

      const dataToSave = { ...data, isPublished: true };
      const hasNameChanged = serverStateRef.current?.userName !== data.userName;
      const isDefaultSlug = data.slug === user.uid;
      const needsNewUrl = (hasNameChanged || isDefaultSlug);

      if (needsNewUrl && data.userName) {
        toast.info("Generating your new public URL...");
        const newSlug = await generateUniqueSlug(data.userName, user.uid);
        dataToSave.slug = newSlug;
      }

      await updatePortfolio({ userId: user.uid, data: dataToSave });
      return dataToSave;
    },
    onSuccess: (savedData) => {
      if (!user) return;

      queryClient.invalidateQueries({ queryKey: ['portfolio', user.uid] });

      // Correct: Publishing is the ONLY action that makes the state "clean".
      serverStateRef.current = savedData;
      setIsDirty(false);
      router.push('/dashboard');
      // Delay toast to ensure it appears after navigation
      setTimeout(() => {
        toast.success("Portfolio published successfully!");
      }, 100);
    },
    onError: (error) => {
      console.error("Publish failed:", error);
      toast.error("Failed to publish portfolio. Please try again.");
    },
  });

  const { mutate: deleteMutation, isPending: isDeleting } = useMutation({
    mutationFn: () => {
      if (!user) throw new Error('User not authenticated');
      return deletePortfolio(user.uid);
    },
    onSuccess: () => {
      if (!user) return;
      queryClient.removeQueries({ queryKey: ['portfolio', user.uid] });
      router.push('/dashboard');
      // Delay toast to ensure it appears after navigation
      setTimeout(() => {
        toast.success("Portfolio discarded. You can choose a new template to start over.");
      }, 100);
    },
    onError: () => {
      setTimeout(() => {
        toast.error("Failed to delete portfolio.");
      }, 100);
    },
  });

  const { mutate: uploadMutation } = useMutation({
    mutationFn: (vars: { file: File, type: 'profile' | 'resume' | 'project', id?: string }) => {
      console.log('🚀 Starting upload:', { type: vars.type, id: vars.id, fileName: vars.file.name });
      dispatch({ type: 'SET_UPLOADING', payload: { type: vars.type, id: vars.id } });
      return uploadFile(vars.file);
    },
    onSuccess: (url, vars) => {
      console.log('✅ Upload successful:', { type: vars.type, id: vars.id, url });

      // Add timestamp to force re-render and cache busting
      const urlWithTimestamp = url.includes('?') ? `${url}&t=${Date.now()}` : `${url}?t=${Date.now()}`;

      toast.success(`${vars.type.charAt(0).toUpperCase() + vars.type.slice(1)} uploaded successfully!`);

      if (vars.type === 'profile') {
        dispatch({ type: 'UPDATE_FIELD', payload: { field: 'profileImageUrl', value: urlWithTimestamp } });
      } else if (vars.type === 'resume') {
        dispatch({ type: 'UPDATE_FIELD', payload: { field: 'resumeUrl', value: url } });
      } else if (vars.type === 'project' && vars.id) {
        console.log('🔍 Looking for project with ID:', vars.id);
        console.log('📋 Current projects:', formData.projects.map(p => ({ id: p.id, title: p.title })));

        const projectIndex = formData.projects.findIndex(p => p.id === vars.id);
        console.log('📍 Found project at index:', projectIndex);

        if (projectIndex !== -1) {
          console.log('🔄 Updating project image at index:', projectIndex);
          dispatch({ type: 'UPDATE_PROJECT', payload: { index: projectIndex, field: 'imageUrl', value: urlWithTimestamp } });
        } else {
          console.error('❌ Project not found with ID:', vars.id);
          toast.error("Project not found. Please try again.");
        }
      }
    },
    onError: (error) => {
      console.error('❌ Upload error:', error);
      toast.error("Upload failed. Please try again.");
    },
    onSettled: () => {
      console.log('🏁 Upload settled, clearing upload state');
      dispatch({ type: 'SET_UPLOADING', payload: null });
    },
  });

  // --- Auto-save effect ---
  useEffect(() => {
    // The auto-save draft functionality.
    if (isDirty) {
      dispatch({ type: 'SET_SAVE_STATUS', payload: 'saving' });
      autoSave(debouncedFormData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedFormData, autoSave, isDirty]);

  // --- Handlers & State ---
  const [dialog, setDialog] = useState({ isOpen: false, onConfirm: () => { } });
  const handlePreview = () => { localStorage.setItem('portfolio-preview', JSON.stringify(formData)); window.open('/portfolio/preview', '_blank'); };
  const handleDeleteConfirm = () => setDialog({ isOpen: true, onConfirm: () => deleteMutation() });

  // Early return if user is not authenticated (e.g., during build time)
  // This is after all hooks to avoid React hooks rules violation
  if (!user) {
    return <div className="flex h-full items-center justify-center"><Loader2 className="h-8 w-8 animate-spin" /></div>;
  }

  return (
    <>
      <div className="h-full flex flex-col bg-muted/20">
        <header className="p-4 border-b bg-background/95 backdrop-blur-sm flex justify-between items-center sticky top-0 z-[60] shadow-sm">
          <PortfolioHeader
            isPublishing={isPublishing}
            isDeleting={isDeleting}
            isDirty={isDirty}
            onPreview={handlePreview}
            onDelete={handleDeleteConfirm}
            onTogglePublish={() => publishMutation(formData)}
          />
        </header>
        <main className="flex-1 overflow-y-auto">
          {formData.templateId === 'profolify-theme-v1' && (
            <ProfolifyTheme isEditing={true} onImageUpload={uploadMutation} />
          )}
          {formData.templateId === 'modern-theme-v1' && (
            <ModernTheme isEditing={true} onImageUpload={uploadMutation} />
          )}
        </main>
      </div>
      <ConfirmationDialog
        isOpen={dialog.isOpen}
        onClose={() => setDialog({ isOpen: false, onConfirm: () => { } })}
        onConfirm={() => { dialog.onConfirm(); setDialog({ isOpen: false, onConfirm: () => { } }); }}
        title="Are you absolutely sure?"
        description="This action is irreversible and will permanently delete all of your portfolio data."
      />
    </>
  );
}

// The page wrapper that fetches data and provides the context
export default function PortfolioPage() {
  const { user } = useAuthStore();
  const router = useRouter();

  const { data: initialData, isLoading } = useQuery<PortfolioData | null>({
    queryKey: ["portfolio", user?.uid || ""],
    queryFn: () => user ? getPortfolio(user.uid) : Promise.resolve(null),
    enabled: !!user,
    staleTime: 5 * 60 * 1000,
  });

  useEffect(() => {
    if (!isLoading && !initialData) {
      toast.error("No portfolio found. Please select a template to begin.");
      router.replace('/choose-template');
    }
  }, [isLoading, initialData, router]);

  if (isLoading || !initialData) {
    return <div className="flex h-full items-center justify-center"><Loader2 className="h-8 w-8 animate-spin" /></div>;
  }

  return (
    <ExportProvider value={false}>
      <EditorProvider initialData={initialData}>
        <PortfolioEditorCore />
      </EditorProvider>
    </ExportProvider>
  );
}
"use client";

import { useAuthStore } from "@/stores/auth-store";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowRight, Eye, Loader2, Download, ExternalLink, LogOut } from "lucide-react";
import { PortfolioData } from "@/lib/types";
import { Input } from "@/components/ui/input";
import { getPortfolio, createPortfolioFromTemplate } from "@/lib/portfolio-api";
import { auth } from "@/lib/firebase";
import { signOut } from "firebase/auth";
import { toast } from "sonner";
import { useState, useEffect } from "react";
import { templates } from "@/lib/templates";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { FullScreenLoader } from "@/components/ui/FullScreenLoader";

// Helper function to delete cookies
const deleteCookie = (name: string) => {
  document.cookie = name + '=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
};

export default function DashboardPage() {
  const { user, isLoaded } = useAuthStore();
  const router = useRouter();
  const queryClient = useQueryClient();
  const [isExporting, setIsExporting] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);

  // Reset navigation state on unmount or route change
  useEffect(() => {
    return () => {
      setIsNavigating(false);
    };
  }, []);
  const [loadingTemplateId, setLoadingTemplateId] = useState<string | null>(null);

  const { data: portfolio, isLoading } = useQuery<PortfolioData | null>({
    queryKey: ["portfolio", user?.uid],
    queryFn: () => getPortfolio(user!.uid),
    enabled: !!user,
  });

  // Sign out handler
  const handleSignOut = () => {
    signOut(auth);
    deleteCookie('firebaseIdToken');
    router.push("/");
    // Delay toast to ensure it appears after navigation
    setTimeout(() => {
      toast.success("You have been signed out.");
    }, 100);
  };

  // Template selection mutation
  const { mutate: selectTemplate } = useMutation({
    mutationFn: createPortfolioFromTemplate,
    onMutate: (variables) => {
      setLoadingTemplateId(variables.templateId);
    },
    onSuccess: (newPortfolio) => {
      queryClient.setQueryData(['portfolio', user?.uid], newPortfolio);
      setIsNavigating(true);
      // Show loading for smooth transition - don't navigate immediately
      setTimeout(() => {
        router.push('/portfolio');
        // Delay toast to ensure it appears after navigation
        setTimeout(() => {
          toast.success("Portfolio created! Let's get started.");
        }, 200);
      }, 800); // Show loading for 800ms before navigation
    },
    onError: () => {
      setIsNavigating(false);
      setLoadingTemplateId(null);
      setTimeout(() => {
        toast.error("Failed to create portfolio. Please try again.");
      }, 100);
    },
    onSettled: () => {
      setLoadingTemplateId(null);
    }
  });

  // Template selection handler
  const handleSelectTemplate = (templateId: string) => {
    if (!user) {
      toast.error("You must be logged in to select a template.");
      return;
    }
    selectTemplate({
      userId: user.uid,
      userEmail: user.email,
      templateId
    });
  };

  const handlePreview = () => {
    if (!portfolio) return;
    localStorage.setItem('portfolio-preview', JSON.stringify(portfolio));
    window.open('/portfolio/preview', '_blank');
  };


  const handleExport = async () => {
    if (!user || !auth.currentUser) {
      toast.error("Authentication error. Please re-login.");
      return;
    }
    setIsExporting(true);
    toast.info("Starting portfolio export... This may take a moment.");

    try {
      const token = await auth.currentUser.getIdToken();
      const response = await fetch('/api/export', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const { error } = await response.json();
        throw new Error(error || "An unknown error occurred during export.");
      }

      // This is the standard browser logic to trigger a file download from a blob
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${portfolio?.slug || 'portfolio'}.zip`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);

      toast.success("Export successful! Check your downloads.");

    } catch (error: unknown) {
      if (error instanceof Error) {
        toast.error(`Export failed: ${error.message}`);
      } else {
        toast.error("Export failed: An unknown error occurred.");
      }
    } finally {
      setIsExporting(false);
    }
  };

  // Show loading overlay when navigating
  if (isNavigating) {
    return <FullScreenLoader text="Setting up your portfolio editor..." />;
  }

  // Show loading while fetching portfolio data or if auth is not loaded
  if (isLoading || !isLoaded) {
    return <FullScreenLoader text="Loading your workspace..." />;
  }

  return (
    <div className="min-h-screen">
      {/* Navigation Bar */}
      <nav className="bg-background border-b ">
        <div className="flex items-center justify-between max-w-7xl mx-auto px-6 py-4">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-brandPrimary to-brandSecondary flex items-center justify-center">
              <span className="text-white font-bold text-sm">P</span>
            </div>
            <h1 className="text-xl font-bold">Profolify</h1>
          </div>

          {/* User Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-10 w-10 rounded-full">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={user?.photoURL ?? undefined} alt={user?.displayName || 'User'} />
                  <AvatarFallback>{user?.displayName?.charAt(0) || 'U'}</AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <div className="flex items-center justify-start gap-2 p-2">
                <div className="flex flex-col space-y-1 leading-none">
                  <p className="font-medium">{user?.displayName}</p>
                  <p className="w-[200px] truncate text-sm text-muted-foreground">
                    {user?.email}
                  </p>
                </div>
              </div>
              <DropdownMenuSeparator />
              {/* <DropdownMenuItem disabled>
                <Settings className="mr-2 h-4 w-4" />
                <span>Settings</span>
              </DropdownMenuItem> */}
              <DropdownMenuItem onClick={handleSignOut}>
                <LogOut className="mr-2 h-4 w-4" />
                <span>Sign out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 py-8">
        {/* Scenario A: User has an existing portfolio */}
        {portfolio && (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold">Welcome back, {user?.displayName}!</h2>
              <p className="text-muted-foreground">Manage your portfolio and track your online presence.</p>
            </div>

            <Card className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold">Portfolio Status</h3>
                  <p className="text-sm text-muted-foreground">
                    Your portfolio is currently {portfolio.isPublished ? 'published and live' : 'a draft'}.
                  </p>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <div className={`w-2 h-2 rounded-full ${portfolio.isPublished ? 'bg-green-500' : 'bg-yellow-500'}`} />
                  <span className="text-muted-foreground">
                    {portfolio.isPublished ? 'Live' : 'Draft'}
                  </span>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Live URL</label>
                  <Input
                    readOnly
                    value={`${typeof window !== 'undefined' ? window.location.origin : ''}/${portfolio.slug}`}
                    className="mt-1"
                  />
                </div>

                <div className="flex flex-wrap gap-3">
                  {portfolio.isPublished ? (
                    <Button asChild>
                      <Link href={`/${portfolio.slug}`} target="_blank">
                        <ExternalLink className="mr-2 h-4 w-4" />
                        View Live
                      </Link>
                    </Button>
                  ) : (
                    <Button onClick={handlePreview} variant="outline">
                      <Eye className="mr-2 h-4 w-4" />
                      Preview
                    </Button>
                  )}

                  <Button asChild variant="secondary">
                    <Link href="/portfolio">
                      Edit Portfolio
                    </Link>
                  </Button>

                  <Button onClick={handleExport} variant="outline" disabled={isExporting}>
                    {isExporting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Download className="mr-2 h-4 w-4" />}
                    Export Site
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* Scenario B: User has no portfolio */}
        {!portfolio && (
          <div className="space-y-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold">Welcome to Profolify, {user?.displayName}!</h2>
              <p className="text-lg text-muted-foreground mt-2">Choose a template to get started.</p>
            </div>

            <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              {templates.map((template) => {
                const isLoadingThisTemplate = loadingTemplateId === template.id;
                return (
                  <Card key={template.id} className="group hover:shadow-lg transition-all duration-300 overflow-hidden">
                    <div className="aspect-video relative bg-muted">
                      <Image
                        src={template.thumbnailUrl}
                        alt={template.name}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <CardHeader>
                      <CardTitle className="text-xl">{template.name}</CardTitle>
                      <CardDescription className="text-base">{template.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Button
                        className="w-full"
                        onClick={() => handleSelectTemplate(template.id)}
                        disabled={!!loadingTemplateId}
                        size="lg"
                      >
                        {isLoadingThisTemplate ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Creating...
                          </>
                        ) : (
                          <>
                            Select {template.name}
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </>
                        )}
                      </Button>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        )}
      </main>
    </div>
  );
}